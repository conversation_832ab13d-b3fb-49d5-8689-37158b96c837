import logging
import sys
from datetime import datetime
from pathlib import Path
import config  

def setup_logger(name: str = "CompanyScorer") -> logging.Logger:
    """
    Sets up a centralized logger for the project.

    The logger will output messages to both the console and a timestamped log file.
    This prevents duplicate handlers by checking if the logger is already configured.

    Args:
        name (str): The name for the logger instance.

    Returns:
        logging.Logger: A configured logger instance.
    """
    # Create the logs directory using a path relative to this file.
    # The `exist_ok=True` argument prevents an error if the directory already exists.
    config.LOGS_DIR.mkdir(parents=True, exist_ok=True)
    
    # Create a timestamped log file name for unique log files per run.
    timestamp = datetime.now().strftime("%Y-%m-%d_%H-%M-%S")
    log_file_path = config.LOGS_DIR / f"scoring_run_{timestamp}.log"
    
    # Get a logger instance. Using a name ensures that all parts of script
    # that use getLogger(name) get the SAME logger instance.
    logger = logging.getLogger(name)
    
    # If the logger already has handlers, it means
    # it was already set up. We return it to prevent adding duplicate handlers,
    # which would cause log messages to be printed multiple times.
    if logger.handlers:
        return logger
        
    # Set the minimum level of messages to handle. INFO is a good default.
    # Levels are: DEBUG, INFO, WARNING, ERROR, CRITICAL
    logger.setLevel(logging.INFO)
    
    # format for log messages to make them readable.
    formatter = logging.Formatter(
        '%(asctime)s - %(name)s - %(levelname)s - [%(filename)s:%(lineno)d] - %(message)s'
    )
    
    # --- Handler 1: Log to a File ---
    # This handler writes log messages to our timestamped file.
    file_handler = logging.FileHandler(log_file_path, encoding='utf-8')
    file_handler.setFormatter(formatter)
    
    # --- Handler 2: Log to the Console ---
    # This handler streams log messages to the standard output (your terminal).
    stream_handler = logging.StreamHandler(sys.stdout)
    stream_handler.setFormatter(formatter)
    
    # Add both configured handlers to the logger.
    logger.addHandler(file_handler)
    logger.addHandler(stream_handler)
    
    logger.info(f"Logger '{name}' initialized. Logging to file: {log_file_path}")
    
    return logger

# Create a single, globally accessible logger instance.
# Other modules in the project can simply do `from logger import log`
log = setup_logger()

if __name__ == '__main__':
    log.info("This is an informational message.")
    log.warning("This is a warning message.")
    log.error("This is an error message from the test block.")
    log.debug("This DEBUG message will NOT appear because the logger's level is set to INFO.")