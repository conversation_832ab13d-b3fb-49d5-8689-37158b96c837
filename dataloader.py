import pandas as pd
from typing import List, Dict, Any
import config
from logger import log

def load_and_sort_companies(industry_name: str) -> List[Dict[str, Any]]:
    """
    Loads company data from the main Excel file, filters for a specific industry,
    and sorts the companies by market cap in descending order.

    Args:
        industry_name (str): The name of the 'Factset_Industry' to filter by.

    Returns:
        List[Dict[str, Any]]: A single list of company dictionaries, sorted by market cap.
                              Each dictionary contains the company's name and market cap.
                              Returns an empty list if the industry is not found or the file is missing.
    """
    log.info(f"Starting data loading process for industry: '{industry_name}'")

    try:
        df = pd.read_excel(config.COMPANY_DATA_FILE)
        log.info(f"Successfully loaded Excel file from: {config.COMPANY_DATA_FILE}")

    except FileNotFoundError:
        log.error(f"FATAL: The data file was not found at {config.COMPANY_DATA_FILE}. Please check the path in config.py.")
        return [] 

    industry_df = df[df['Factset_Industry'] == industry_name].copy()

    if industry_df.empty:
        log.warning(f"No companies found for industry '{industry_name}'. Please check the industry name.")
        return []

    log.info(f"Found {len(industry_df)} companies in the '{industry_name}' industry.")

    # Sort the DataFrame by 'Company_Mcap_USD' in descending order (largest first)
    industry_df_sorted = industry_df.sort_values(by='Company_Mcap_USD', ascending=False)
    log.info("Successfully sorted companies by market cap (descending).")
    
    # Convert the sorted DataFrame to a list of dictionaries
    # We only keep the columns we need for the rest of the pipeline
    required_columns = ['company_name', 'Company_Mcap_USD']
    company_list = industry_df_sorted[required_columns].to_dict('records')
    
    formatted_company_list = [
        {
            'name': company['company_name'],
            'market_cap_usd': company['Company_Mcap_USD']
        }
        for company in company_list
    ]

    log.info(f"Data loading complete. Returning {len(formatted_company_list)} sorted companies.")
    return formatted_company_list



if __name__ == '__main__':
    test_industry = 'Motor Vehicles'
    sorted_companies = load_and_sort_companies(test_industry)

    if sorted_companies:
        print(f"\n--- Dataloader Test Results for '{test_industry}' ---")
        print(f"Total companies found and sorted: {len(sorted_companies)}")
        print("\nTop 5 companies by market cap:")
        for i, comp in enumerate(sorted_companies[:5]):
            print(f"  {i+1}. {comp['name']} (Market Cap: ${comp['market_cap_usd']:,.2f}M)")
        
        print("\n--- Test Complete ---")
    else:
        print(f"\nTest failed or no companies found for industry '{test_industry}'.")