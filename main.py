import config
import dataloader
import data_retriever
import scorer
from logger import log

def run_pipeline_for_topic(industry_name: str, topic: str, conversion_factor: float):
    """
    Executes the entire data retrieval and scoring pipeline for a single topic.

    Args:
        industry_name (str): The industry to analyze (e.g., "Semiconductors").
        topic (str): The topic for analysis ('capacity' or 'employment').
    """
    log.info(f"================== STARTING PIPELINE FOR TOPIC: {topic.upper()} ==================")
    
    # 1. Load and sort all companies for the industry by market cap
    log.info(f"Step 1: Loading and sorting companies for industry: '{industry_name}'")
    all_companies = dataloader.load_and_sort_companies(industry_name)
    
    if not all_companies:
        log.error(f"No companies found for industry '{industry_name}'. Aborting pipeline for this topic.")
        return

    # This list will hold all scored results from all batches for this topic
    all_scored_results_for_topic = []

    # 2. Process all companies in batches
    log.info(f"Step 2: Starting batch processing of {len(all_companies)} companies.")
    for i in range(0, len(all_companies), config.SCORING_BATCH_SIZE):
        batch_of_companies = all_companies[i : i + config.SCORING_BATCH_SIZE]
        batch_num = i // config.SCORING_BATCH_SIZE + 1
        log.info(f"--- Processing Batch {batch_num} ({len(batch_of_companies)} companies) ---")

        # 3. Get unified data (RAG + Web Search) for this batch
        log.info(f"Step 3: Retrieving unified data for Batch {batch_num}.")
        unified_data_batch = data_retriever.get_unified_data_for_companies(
            companies=batch_of_companies,
            topic=topic
        )
        
        if not unified_data_batch:
            log.warning(f"Could not retrieve any data for Batch {batch_num}. Skipping to next batch.")
            continue

        # 4. Score the unified data for this batch
        log.info(f"Step 4: Scoring Batch {batch_num}.")
        scored_batch = scorer.get_scores_for_companies(unified_data_batch, topic, conversion_factor)

        # 5. Enrich the scored data with the original market cap
        # The scoring LLM doesn't return the market cap, so we add it back here
        # for the final report.
        # market_cap_map = {
        #     company['name']: company['market_cap_usd'] 
        #     for company in batch_of_companies
        # }
        
        # for scored_company in scored_batch:
        #     company_name = scored_company.get('company')
        #     if company_name in market_cap_map:
        #         scored_company['market_cap_usd'] = market_cap_map[company_name]
        
        all_scored_results_for_topic.extend(scored_batch)
        log.info(f"--- Finished processing Batch {batch_num} ---")

    # 6. Save the final, aggregated results to an Excel file
    log.info(f"Step 6: Saving all {len(all_scored_results_for_topic)} scored results to Excel.")
    scorer.save_scores_to_excel(
        scored_data=all_scored_results_for_topic,
        industry_name=industry_name,
        topic=topic
    )
    log.info(f"================== PIPELINE COMPLETE FOR TOPIC: {topic.upper()} ==================\n")


if __name__ == '__main__':
    # Define the mapping of industries to their conversion factors
    INDUSTRY_CONVERSION_FACTORS = {
        "Motor Vehicles": 1.0,
        "Semiconductors": 1.5,
        "Industrial Machinery": 0.8
        # Add other industries and their factors here
    }

    # Define which industries you want to run the pipeline for
    TARGET_INDUSTRIES = ["Motor Vehicles", "Semiconductors"]

    log.info("STARTING FULL ANALYSIS FOR MULTIPLE INDUSTRIES")
    log.info(f"Target Industries: {', '.join(TARGET_INDUSTRIES)}")
    log.info("=" * 70)

    # Loop through each target industry and run the full pipeline
    for industry in TARGET_INDUSTRIES:
        log.info(f"########### PROCESSING INDUSTRY: {industry.upper()} ###########")
        
        # Look up the conversion factor for the current industry.
        # Use .get() with a default of 1.0 in case the industry is not in our map.
        factor = INDUSTRY_CONVERSION_FACTORS.get(industry, 1.0)
        log.info(f"Using capacity conversion factor: {factor}")

        # Run the pipeline for the 'capacity' topic with the specific factor
        run_pipeline_for_topic(
            industry_name=industry, 
            topic='capacity', 
            conversion_factor=factor
        )
        
        # # Run the pipeline for the 'employment' topic (factor is passed but unused)
        # run_pipeline_for_topic(
        #     industry_name=industry, 
        #     topic='employment', 
        #     conversion_factor=factor
        # )
    
    log.info("=" * 70)
    log.info("ALL PIPELINES HAVE COMPLETED SUCCESSFULLY.")