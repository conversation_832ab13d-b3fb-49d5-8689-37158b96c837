#---- This file contains All the prompts, pydantic models and Json schemas used in project-------

#--- RAG Prompts-----
CAP_EXP_SYSTEM_PROMPT_SEC = """\
You are a helpful assistant, answering `USER QUESTION` based on provided `CONTEXT`by the user.
In the user message, you will be provided the following:
- `USER QUESTION`: In this the user will ask about manufacturing capacity expansion or new plant opening plans in the United States for a particular company. The user will also ask about manufacturing capacity reduction or existing plan shutdown in the United States for a particular company.
- `CONTEXT`: Excerpts from SEC filings (10-K and 10-Q) of the company which you have to use for answering this question.

Your objective is to identify all information related to manufacturing capacity expansion or new plant openings, or capacity reduction or shutdown of existing plant in the United States from the provided `CONTEXT` and return the ouptput in the structured format.

Follow these strict guidelines while answering the question:

1. Use Only Provided Context: 
   - Base your answer exclusively on the given text chunks.
   - Do not rely on any external knowledge or assumptions.

2a. Identify Capacity-Expansion Plans: 
   - Locate information in the given context describing intentions, plans, or commitments to:
	- Expand existing factories in the United States
        - Build or commission new manufacturing facilities in the United States.
   - Quantify Every Expansion Plan:
        - Extract and report numerical metrics such as allocated capex for new manufacturing capacity, planned plant capacity, or other relevant figures if available in the given context. (e.g., "...investing $X billion to expand factory", "...setting up a new Y sq ft facility" etc.)

2b. Identify Capacity-Reduction Plans: 
   - Locate information in the given context describing intentions, plans, or commitments to:
	- Reduce or divest existing factories in the United States
        - Shutdown or de-commission existing manufacturing facilities in the United States

   - Quantify Every Reduction Plan:
        - Extract and report numerical metrics such as manufacturing capacity being planned for shutdown or de-commissioning, or other relevant figures if available in the given context. (e.g., "... reducing capacity of X factory", "...closing Y sq ft facility" etc.)


3. Output Format & Fields:
    - Your response must be in a given structured format with the following fields:

    - `"company"`: The name of the company being analyzed.
    - `"capacity_expansion_plans"`: A list of individual U.S.-based manufacturing expansion initiatives, where each item includes:
         - `"plan_details"`: A detailed natural language description of the plan with quantification if available.
         - `"title"`: report type (10-K, 10-Q or 8-K) provided in context.
         - `"publishing_date"`: Filing date in `YYYY-MM-DD` format.
         - `"url"`: URL of the original SEC filing provided in context.
    - `"capacity_reduction_plans"`: A list of individual U.S.-based manufacturing reduction plans, where each item includes:
         - `"plan_details"`: A detailed natural language description of the plan with quantification if available.
         - `"title"`: report type (10-K, 10-Q or 8-K) provided in context.
         - `"publishing_date"`: Filing date in `YYYY-MM-DD` format.
         - `"url"`: URL of the original SEC filing provided in context.

4. Return only this structured output, without any additional commentary or explanation.
"""


EMP_GEN_SYSTEM_PROMPT_SEC = """\
You are a helpful assistant, answering `USER QUESTION` based on provided `CONTEXT`by the user.
In the user message, you will be provided the following:
- `USER QUESTION`: In this the user will ask about employment generation plans in the United States for a particular company. The user will also ask about employment reduction plans in the United States for a particular company.

- `CONTEXT`: Excerpts from SEC filings (10-K and 10-Q) of the company which you have to use for answering this question.
Your objective is to identify all information related to employment generation or employment reduction in the United States from the provided `CONTEXT` and return the output in structured format.

Follow these strict guidelines while answering the question:

1. Use Only Provided `CONTEXT`: 
   - Base your answer exclusively on the given text chunks.
   - Do not rely on any external knowledge or assumptions.

2a. Identify Employment-Generation Plans:  
     - Locate any information in the given `CONTEXT` describing hiring targets, job-creation commitments, or intended workforce increases in the United States.
     - Quantify Every Plan:
     	- Quantify by citing numeric metrics if available in the `CONTEXT`.(e.g., "1,000 new employees", "2,500 jobs over three years" etc.).

2b. Identify Employment-Reduction Plans:  
     - Locate any information in the given `CONTEXT` describing layoff targets or workforce reduction plans, or intended workforce decreases in the United States.
     - Quantify Every Plan:
     	- Quantify by citing numeric metrics if available in the `CONTEXT`.(e.g., "1,000 employees to be laid off", "2,500 jobs reduced over three years" etc.).


3. Output Format & Fields:
   - Your response must be in a given structured format with the following fields:

   - `"company"`: The name of the company being analyzed.
   - `"employment_generation_plans"`: A list of individual U.S. based employment generation initiatives, where each item includes:
      - `"plan_details"`: A detailed natural language description of the plan.
      - `"title"`: report type (10-K, 10-Q ) provided in context.
      - `"publishing_date"`: Filing date in `YYYY-MM-DD` format.  
      - `"url"`: URL of the original SEC filing provided in context.
      - `"confidence_score"`: Put exact score of 1 for each plan.
   - `"employment_reduction_plans"`: A list of individual U.S. based employment reduction plans, where each item includes:
      - `"plan_details"`: A detailed natural language description of the plan.
      - `"title"`: report type (10-K, 10-Q) provided in context.
      - `"publishing_date"`: Filing date in `YYYY-MM-DD` format.
      - `"url"`: URL of the original SEC filing provided in context.
      - `"confidence_score"`: Put exact score of 1 for each plan.

Return only this structured output, without any additional commentary or explanation.
"""

#---- Web Search Prompts-----

CAP_EXP_SYSTEM_PROMPT_WEB = """\
You are a research assistant with access to web-search. The user will ask about manufacturing capacity epansion / reduction or new plant opening / shutdown plans in the United States for a particular company. You need to present the output in structured json format with following fields:

	- `company`: name of the target company.
	- `capacity_expansion_plans`: list of objects with `plan_details` regarding capacity expansion or new plant opening, `publishing_date, publishing_agency, title, url and confidence_score`.
	- `capacity_reduction_plans`: list of objects with `plan_details` regarding capacity reduction or shutdown of existing plants, publishing_date, publishing_agency, title, url and confidence_score.
To calculate `confidence score` follow the below guidelines:
	- A score between 0 and 0.9 suggesting confidence in the information given in plan details and source, calculated as follows:  
    	- Source Reliability (0.0 - 0.4):  
         0.4=Company's official press release; 0.3=Tier-1 industry press; 0.2=Blog/aggregator; 0.0=Unverified forum/social media  
    	- Specificity Score(0.0 - 0.5):  
         0.5=Direct management quote with numeric metric; 0.4=Exact excerpt paraphrased; 0.3=Summary statement without numbers; 0.2=Analyst commentary; 0.0= pure Speculation  
    	- `confidence_score = Source Reliability + Specificity Score`
"""

EMP_GEN_SYSTEM_PROMPT_WEB = """\
You are a research assistant with access to web-search. The user will ask about employment generation / reduction plans in the United States for a particular company. You need to present the output in structured json format with following fields:

	- `company`: name of the target company.
	- `capacity_expansion_plans`: list of objects with `plan_details` regarding employment generation, hiring, job creation etc., `title`, `publishing_date, publishing_agency, , url and confidence_score`.
	- `capacity_reduction_plans`: list of objects with `plan_details` regarding employment reduction, layoffs, job cuts etc., `title`, `publishing_date`, `publishing_agency`, `url` and `confidence_score`.
    
    To calculate `confidence score` follow the below guidelines:
	- A score between 0 and 0.9 suggesting confidence in the information given in plan details and source, calculated as follows:  
    	- Source Reliability (0.0 - 0.4):  
         0.4=Company's official press release; 0.3=Tier-1 industry press; 0.2=Blog/aggregator; 0.0=Unverified forum/social media  
    	- Specificity Score(0.0 - 0.5):  
         0.5=Direct management quote with numeric metric; 0.4=Exact excerpt paraphrased; 0.3=Summary statement without numbers; 0.2=Analyst commentary; 0.0= pure Speculation  
    	- `confidence_score = Source Reliability + Specificity Score`
"""

#---- Scoring Prompts-----

SCORES_SYSTEM_PROMPT_CAP = """\
You are an expert research analyst evaluating companies'U.S based manufacturing capacity expansion and reduction plans. 
You are provided with structured JSON data for multiple companies. For each company, you will be given:
- `capacity_expansion_plans`
- `capacity_reduction_plans`

Each plan detail has a `confidence_score` (0-1), representing the reliability of its source and specificity of the information.

Your task is to follow a strict, sequential reasoning process to quantify both non-annualized (total) and annualized capacity expansion and reduction magnitudes (in million dollars), while incorporating confidence scores, annualization for multi-year plans, and rigorous deduplication.

IMPORTANT EXECUTION REQUIREMENT:
As you execute the following steps (1-3), you must document every action, intermediate calculation, and decision in `rationale_capacity_magnitude` in the exact order they are performed.  
This rationale acts as a computation log and should include:
  - Raw extraction details.
  - Conversions (if any).
  - Confidence score applications.
  - Annualization logic.
  - Deduplication and overlap checks.
  - Intermediate sums leading to the final values.

Once step 3 is complete you will have the final computed capacity expansion and reduction magnitudes (both annualized and total) you should use the same values into their respective JSON fields without recomputation or modification.

INSTRUCTIONS (STRICT SEQUENTIAL STEPS):

STEP 1: Extract & Quantify Plan Values
For each company:
  - Review all `plan_details` under `capacity_expansion_plans` and `capacity_reduction_plans`.
  - Identify quantifiable metrics such as:
    - Dollar investments (e.g., "$200 million investment").
    - Facility size (e.g., "200,000 sq. ft. plant").
    - Production/output units (e.g., "50,000 units per year").
  - Document each plan's raw value in `rationale_capacity_magnitude`.

STEP 2: Apply Rules to Each Plan (and document in rationale)
1. Dollar Extraction and Conversion:
  - If explicit dollar values are mentioned for any plan, use that directly.
  - If explicit dollar value for a particular plan is not provided but the non dollar metric of square footage is mentioned convert them to dollar values using the following conversion factor:
    - Square footage → dollars: ${conversion_factor} per sq. ft.
  - If a plan is not quantifiable or contains vague language (e.g., "significant expansion"), treat its value as 0.
  - Document the final dollar value (actual or converted) and the conversion calculation when applicable for each plan in `rationale_capacity_magnitude`.

2. Confidence Score Weighting:
  - Multiply the quantified dollar value of each plan by its associated `confidence_score`.
    - Example: If a plan is $500M and its confidence score is 0.8, its weighted value = $500M * 0.8 = $400M.
  - Document the weighted value clearly in the `rationale_capacity_magnitude`.

3. Annualization of Multi-Year Plans:
  - After applying confidence weighting:
    - If the plan explicitly states it is multi-year and specifies duration (e.g., "over 5 years"), divide the weighted value by the stated number of years.
      - Example: $400M ÷ 5 years = $80M annualized.
      If the plan is multi-year but duration is NOT mentioned, divide the weighted value by 3 (default assumption).
    - If the plan is single-year (or unspecified and clearly immediate), keep it as is.
  - Document each annualization step in `rationale_capacity_magnitude`.

4. Deduplication and Avoiding Double Counting:
  - Carefully check if multiple plans describe the same project or investment (same plant, location, capacity, identical or near-identical amounts described differently).
  - If duplicates are found:
      - Keep only one instance of the plan and discard others.
      - If duplicates have different confidence scores, retain the version with the highest confidence score.
  - Explicitly state in `rationale_capacity_magnitude` whenever a duplicate is identified and removed.
  - Ensure that no plan or investment is counted more than once in any scenario.

5. Avoiding Overlaps Across Plans:
  - If multiple plans overlap (e.g., a total program amount and its sub-projects are both listed), count only the most specific and accurate figure to avoid inflating totals. Document this step in `rationale_capacity_magnitude` as well.
  
5. Zero for Non-Quantifiable Plans:
  - Explicitly document in `rationale_capacity_magnitude`: `"Plan not quantifiable → counted as $0"` if applicable.

STEP 3: Aggregate Magnitudes and Finalize Rationale
After applying all rules:
  - Compute the following four values and log them at the end of `rationale_capacity_magnitude`:
    1. total_capacity_expansion_magnitude = Sum of all, weighted (by confidence score) expansion plans (non-annualized).
    2. capacity_expansion_magnitude_annualized = Sum of all, weighted (by confidence score), annualized expansion plans.
    3. total_capacity_reduction_magnitude = Sum of all, weighted (by confidence score) reduction plans (non-annualized).
    4. capacity_reduction_magnitude_annualized = Sum of all, weighted (by confidence score), annualized reduction plans.
  - Present all sums in million dollars (no commas, no symbols).
  - End `rationale_capacity_magnitude` with clear final statements:
    - "Final total expansion magnitude = ..."
    - "Final annualized expansion magnitude = ..."
    - "Final total reduction magnitude = ..."
    - "Final annualized reduction magnitude = ..."

STEP 4: Populate Final Fields
After `rationale_capacity_magnitude` is complete:
  - Copy the final four numeric values exactly (no recalculation) into their respective fields in the output JSON:
    - `total_capacity_expansion_magnitude`
    - `capacity_expansion_magnitude_annualized`
    - `total_capacity_reduction_magnitude`
    - `capacity_reduction_magnitude_annualized`

OUTPUT FORMAT:
Return output in strict JSON format:
{{
  "companies": [
    {{
      "company": "<company name>",
      "rationale_capacity_magnitude": "<detailed sequential computation log from Steps 1-3>",
      "total_capacity_expansion_magnitude": <numeric in million dollars>,
      "capacity_expansion_magnitude_annualized": <numeric in million dollars>,
      "total_capacity_reduction_magnitude": <numeric in million dollars>,
      "capacity_reduction_magnitude_annualized": <numeric in million dollars>
    }},
    ...
  ]
}}

"""

SCORES_SYSTEM_PROMPT_EMP = """\
You are an expert research analyst evaluating companies'employment generation (hiring) and employment reduction (layoffs) plans in United States.  
You are provided with structured JSON data for multiple companies. For each company, you will be given:  
- `employment_generation_plans`  
- `employment_reduction_plans`  

Each plan detail includes a `confidence_score` (0-1), representing the reliability of its source and specificity of the information.  

Your task is to follow a strict, sequential reasoning process to quantify both non-annualized (total) and annualized employment generation and reduction magnitudes (in number of jobs), while incorporating confidence scores, multi-year adjustments, and rigorous deduplication.

IMPORTANT EXECUTION REQUIREMENT:
As you execute the steps, document every action, intermediate calculation, and decision in `rationale_employment_magnitude` in the exact order performed.  
The rationale must include:
- Raw job numbers from text.
- Confidence score applications.
- Annualization logic.
- Deduplication and overlap checks.
- Intermediate sums leading to the final results.

Once step 2 is complete you will have the final computed employment generation and reduction magnitudes (both annualized and total) you should use the same values into their respective JSON fields without recomputation or modification.

INSTRUCTIONS (STRICT SEQUENTIAL STEPS):

STEP 1: Extract, Process, and Document Employment Plans
For each company:
1. Job Number Extraction:
   - Review all `plan_details` in `employment_generation_plans` and `employment_reduction_plans`.
   - Identify explicit job counts (e.g., "Hiring 2,000 employees", "Laying off 500 workers").
   - If expressed as a percentage reduction (e.g., "reduce workforce by 8%"), convert to absolute job count if headcount is provided elsewhere in context.
   - If job count is vague (e.g., "significant hiring"), treat it as 0 jobs.
   - Log each plan's raw job number in `rationale_employment_magnitude`.

2. Confidence Score Weighting:
   - Multiply each job count by its associated `confidence_score`.  
     Example: 1,000 jobs * 0.8 = 800 jobs.  
   - Log both the raw and weighted values in `rationale_employment_magnitude`.

3. Annualization for Multi-Year Plans:
   - After applying confidence weighting:
    - If the plan explicitly states it is multi-year and specifies duration (e.g., "over 5 years"), divide the weighted value by the stated number of years.
       Example: 800 jobs ÷ 4 = 200 (annualized).
     - If the plan is multi-year but duration is NOT mentioned, divide the weighted value by 3 (default assumption).
     - If the plan is single-year (or unspecified and clearly immediate), keep it as is.
   - Log each annualization calculation clearly in `rationale_employment_magnitude`.

4. Deduplication and Avoiding Double Counting:
  - Carefully check if multiple plans describe the same project or investment (same plant, location, capacity, identical or near-identical amounts described differently).
  - If duplicates are found:
      - Keep only one instance of the plan and discard others.
      - If duplicates have different confidence scores, retain the version with the highest confidence score.
  - Explicitly state in `rationale_capacity_expansion` whenever a duplicate is identified and removed.
  - Ensure that no plan or investment is counted more than once in any scenario.

4. Deduplication & Overlap Handling:
   - Check if the same plan appears multiple times (e.g., different sources reporting the same hiring event):
   - If duplicates are found:
      - Keep only one instance of the plan and discard others.
      - If duplicates have different confidence scores, retain the version with the highest confidence score.
   - Explicitly state in `rationale_employment_magnitude` whenever a duplicate is identified and removed.

   - If overlaps exist (e.g., a total hiring program vs. its subcomponents):
     - Retain the most specific/accurate entry to avoid double counting.
     - Explain exclusions in `rationale_employment_magnitude`.

5. Zero Handling for Non-Quantifiable Plans:
   - For unquantifiable plans, explicitly state: `"Plan not quantifiable → counted as 0 jobs"`.

STEP 2: Aggregate Magnitudes & Finalize Rationale
After applying all rules:
- Compute and log:
  1. total_employment_generation_magnitude = Sum of all unique, weighted (by confidence score) job creation plans (non-annualized).
  2. annualized_employment_generation_magnitude = Sum of all unique, weighted (by confidence score), annualized job creation plans.
  3. total_employment_reduction_magnitude = Sum of all unique, weighted (by confidence score) job reduction plans (non-annualized).
  4. annualized_employment_reduction_magnitude = Sum of all unique, weighted (by confidence score), annualized job reduction plans.

- Present sums in integer job counts, round off in case of decimal numbers.
- Conclude rationale with:
  - "Final total employment generation = ..."
  - "Final annualized employment generation = ..."
  - "Final total employment reduction = ..."
  - "Final annualized employment reduction = ..."

STEP 3: Populate Final Fields
After `rationale_employment_magnitude` is complete:
- Copy the final four numeric values exactly (no recalculation) into their respective fields in the output JSON::
  - `total_employment_generation_magnitude`
  - `annualized_employment_generation_magnitude`
  - `total_employment_reduction_magnitude`
  - `annualized_employment_reduction_magnitude`

OUTPUT FORMAT:
Return output in strict JSON:
{{
  "companies": [
    {{
      "company": "<company name>",
      "rationale_employment_magnitude": "<detailed sequential computation log>",
      "total_employment_generation_magnitude": <integer jobs>,
      "annualized_employment_generation_magnitude": <integer jobs>,
      "total_employment_reduction_magnitude": <integer jobs>,
      "annualized_employment_reduction_magnitude": <integer jobs>
    }},
    ...
  ]
}}

"""

#---- Pydantic Models-----
import copy
from typing import List
from pydantic import BaseModel, Field

class SECCapacityExpansionPlan(BaseModel):
    plan_details: str = Field(..., description="Detailed description of the capacity expansion plan")
    title: str = Field(..., description="report type (10-K, 10-Q) provided in context.")
    publishing_date: str = Field(..., description="Date in YYYY-MM-DD format")
    url: str = Field(..., description="URL of the original SEC filing provided in context.")

class SECCapacityReductionPlan(BaseModel):
    plan_details: str = Field(..., description="Detailed description of the capacity reduction plan")
    title: str = Field(..., description="report type (10-K, 10-Q) provided in context.")
    publishing_date: str = Field(..., description="Date in YYYY-MM-DD format")
    url: str = Field(..., description="URL of the original SEC filing provided in context.")
    
class SECCompanyExpansionData(BaseModel):
    company: str = Field(..., description="Name of the company")
    capacity_expansion_plans: List[SECCapacityExpansionPlan]
    capacity_reduction_plans: List[SECCapacityReductionPlan]


class SECEmploymentGenerationPlan(BaseModel):
    plan_details: str = Field(..., description="Detailed description of the employment generation plan")
    title: str = Field(..., description="report type (10-K, 10-Q) provided in context.")
    publishing_date: str = Field(..., description="Date in YYYY-MM-DD format")
    url: str = Field(..., description="URL of the original SEC filing provided in context.")
    confidence_score: float = Field(..., description="Put exact score of 1 for each plan.")

class SECEmploymentReductionPlan(BaseModel):
    plan_details: str = Field(..., description="Detailed description of the employment reduction plan")
    title: str = Field(..., description="report type (10-K, 10-Q) provided in context.")
    publishing_date: str = Field(..., description="Date in YYYY-MM-DD format")
    url: str = Field(..., description="URL of the original SEC filing provided in context.")
    confidence_score: float = Field(..., description="Put exact score of 1 for each plan.")
    
class SECCompanyEmploymentData(BaseModel):
    company: str = Field(..., description="Name of the company")
    employment_generation_plans: List[SECEmploymentGenerationPlan]
    employment_reduction_plans: List[SECEmploymentReductionPlan]


class WebCapacityExpansionPlan(BaseModel):
    plan_details: str = Field(..., description="Detailed description of the capacity expansion plan")
    title: str = Field(..., description="title of the source from where the information is extracted.")
    publishing_date: str = Field(..., description="Publishing Date of the source in YYYY-MM-DD format")
    publishing_agency: str = Field(..., description="Name of the publishing agency of the source")
    url: str = Field(..., description="URL of the source")
    confidence_score: float = Field(..., description="A score between 0 and 0.9 calculated as Source Reliability (0.0–0.4) + Specificity Score(0.0–0.5).")

class WebCapacityReductionPlan(BaseModel):
    plan_details: str = Field(..., description="Detailed description of the capacity reduction plan")
    title: str = Field(..., description="title of the source from where the information is extracted.")
    publishing_date: str = Field(..., description="Publishing Date of the source in YYYY-MM-DD format")
    publishing_agency: str = Field(..., description="Name of the publishing agency of the source")
    url: str = Field(..., description="URL of the source")
    confidence_score: float = Field(..., description="A score between 0 and 0.9 calculated as Source Reliability (0.0–0.4) + Specificity Score(0.0–0.5).")

class WebCompanyExpansionData(BaseModel):
    company: str = Field(..., description="Name of the company")
    capacity_expansion_plans: List[WebCapacityExpansionPlan]
    capacity_reduction_plans: List[WebCapacityReductionPlan]


class WebEmploymentGenerationPlan(BaseModel):
    plan_details: str = Field(..., description="Detailed description of the employment generation plan")
    title: str = Field(..., description="title of the source from where the information is extracted.")
    publishing_date: str = Field(..., description="Publishing Date of the source in YYYY-MM-DD format")
    publishing_agency: str = Field(..., description="Name of the publishing agency of the source")
    url: str = Field(..., description="URL of the source")
    confidence_score: float = Field(..., description="A score between 0 and 0.9 calculated as Source Reliability (0.0-0.4) + Specificity Score(0.0-0.5).")

class WebEmploymentReductionPlan(BaseModel):
    plan_details: str = Field(..., description="Detailed description of the employment reduction plan")
    title: str = Field(..., description="title of the source from where the information is extracted.")
    publishing_date: str = Field(..., description="Publishing Date of the source in YYYY-MM-DD format")
    publishing_agency: str = Field(..., description="Name of the publishing agency of the source")
    url: str = Field(..., description="URL of the source")
    confidence_score: float = Field(..., description="A score between 0 and 0.9 calculated as Source Reliability (0.0-0.4) + Specificity Score(0.0-0.5).")

class WebCompanyEmploymentData(BaseModel):
    company: str = Field(..., description="Name of the company")
    employment_generation_plans: List[WebEmploymentGenerationPlan]
    employment_reduction_plans: List[WebEmploymentReductionPlan]

#----- Json Schemas-----

scores_output_format_cap = {
    "format": {
        "type": "json_schema",
        "name": "company_capacity_magnitudes",
        "schema": {
            "type": "object",
            "properties": {
                "companies": {
                    "type": "array",
                    "items": {
                        "type": "object",
                        "properties": {
                            "company": {
                                "type": "string",
                                "description": "Name of the company being evaluated."
                            },
                            "rationale_capacity_magnitude": {
                                "type": "string",
                                "description": "Detailed sequential computation log from Steps 1-3."
                            },
                            "total_capacity_expansion_magnitude": {
                                "type": "number",
                                "description": "Aggregate quantified value of capacity expansion in dollars, non-annualized."
                            },
                            "capacity_expansion_magnitude_annualized": {
                                "type": "number",
                                "description": "Aggregate quantified value of capacity expansion in dollars, annualized."
                            },
                            "total_capacity_reduction_magnitude": {
                                "type": "number",
                                "description": "Aggregate quantified value of capacity reduction in dollars, non-annualized."
                            },
                            "capacity_reduction_magnitude_annualized": {
                                "type": "number",
                                "description": "Aggregate quantified value of capacity reduction in dollars, annualized."
                            }
                        },
                        "required": [
                            "company",
                            "rationale_capacity_magnitude",
                            "total_capacity_expansion_magnitude",
                            "capacity_expansion_magnitude_annualized",
                            "total_capacity_reduction_magnitude",
                            "capacity_reduction_magnitude_annualized"
                        ],
                        "additionalProperties": False
                    }
                }
            },
            "required": [
                "companies"
            ],
            "additionalProperties": False
        }
    }
}



scores_output_format_emp = {
    "format": {
        "type": "json_schema",
        "name": "company_employment_magnitudes",
        "schema": {
            "type": "object",
            "properties": {
                "companies": {
                    "type": "array",
                    "items": {
                        "type": "object",
                        "properties": {
                            "company": {
                                "type": "string",
                                "description": "Name of the company being evaluated."
                            },
                            "rationale_employment_magnitude": {
                                "type": "string",
                                "description": "Detailed sequential computation log from Steps 1-2."
                            },
                            "total_employment_generation_magnitude": {
                                "type": "number",
                                "description": "Aggregate quantified value of employment generation in number of jobs, non-annualized."
                            },
                            "annualized_employment_generation_magnitude": {
                                "type": "number",
                                "description": "Aggregate quantified value of employment generation in number of jobs, annualized."
                            },
                            "total_employment_reduction_magnitude": {
                                "type": "number",
                                "description": "Aggregate quantified value of employment reduction in number of jobs, non-annualized."
                            },
                            "annualized_employment_reduction_magnitude": {
                                "type": "number",
                                "description": "Aggregate quantified value of employment reduction in number of jobs, annualized."
                            }
                        },
                        "required": [
                            "company",
                            "rationale_employment_magnitude",
                            "total_employment_generation_magnitude",
                            "annualized_employment_generation_magnitude",
                            "total_employment_reduction_magnitude",
                            "annualized_employment_reduction_magnitude"
                        ],
                        "additionalProperties": False
                    }
                }
            },
            "required": [
                "companies"
            ],
            "additionalProperties": False
        }
    }
}


#---- Capacity prompt query template and Employment prompt query template-----

CAPACITY_PLANS_QUERY_TEMPLATES = [
    "Manufacturing capacity expansion plans of {company_name} in the United States",
    "Planned or ongoing increases in production or manufacturing facilities for {company_name} in the US",
    "New plant or factory openings by {company_name} in the United States",
    "Planned construction of new manufacturing or production facilities for {company_name} in the US",
    "Shutdown or closure of plants or factories by {company_name} in the United States",
    "Capacity reduction or downsizing initiatives of {company_name} in the United States",
    "Future manufacturing footprint changes (openings or closures) for {company_name} in the US",
    "Capital investment or expansion announcements related to US manufacturing for {company_name}",
    "US facility construction, expansion, or shutdown initiatives of {company_name}",
    "Manufacturing plant upgrades, expansions, or decommissioning for {company_name} in the United States"
]

EMPLOYMENT_PLANS_QUERY_TEMPLATES = [
    "Employment generation or workforce expansion plans of {company_name} in the United States",
    "Hiring initiatives or recruitment drives announced by {company_name} in the US",
    "Plans for adding new jobs or increasing employee headcount at {company_name} in the United States",
    "Workforce expansion or hiring plans in US facilities of {company_name}",
    "Layoffs, downsizing, or workforce reduction plans of {company_name} in the United States",
    "Restructuring or reorganization plans impacting employment at {company_name} in the United States",
    "Hiring freezes or reduction in recruitment activities at {company_name} in the US",
    "Future workforce or staffing changes (expansion or reduction) planned by {company_name} in the United States",
    "Employment-related changes linked to facility openings, closures, or expansions of {company_name} in the US"
]
#----- User questions and user prompts-----

USER_PROMPT_TEMPLATE_CAP = """
USER QUESTION:
Please identify and extract all manufacturing capacity expansion and reduction plans for the company {company_name} in the United States from the context provided below.

For EXPANSION, look for information related to:
- Building or commissioning new manufacturing facilities.
- Expanding existing factories or adding new production lines.
- Significant capital expenditures (capex) allocated to increasing production capacity.

For REDUCTION, look for information related to:
- Shutting down, idling, or closing existing manufacturing facilities.
- Consolidating factory operations or reducing production output.
- Divesting or selling manufacturing assets.

Base your answer exclusively on the provided context.

CONTEXT:
{context_string}
"""

USER_PROMPT_TEMPLATE_EMP = """
USER QUESTION:
Please identify and extract all employment generation (hiring) and employment reduction (layoffs, downsizing) plans for the company {company_name} in the United States from the context provided below.

For EMPLOYMENT GENERATION (HIRING), look for information related to:
- Announcements of new job creation or workforce expansion.
- Hiring drives, recruitment initiatives, or plans to increase headcount.
- Employment growth linked to new facility openings or expansions.

For EMPLOYMENT REDUCTION (LAYOFFS), look for information related to:
- Layoffs, downsizing, job cuts, or workforce reduction initiatives.
- Hiring freezes or reduced recruitment activity.
- Employment reductions linked to restructuring, plant closures, or cost-cutting measures.

Base your answer exclusively on the provided context.

CONTEXT:
{context_string}
"""


WEB_USER_PROMPT_CAP = "Research extensively and find all publicly announced manufacturing capacity expansions, reductions, new plant openings, or shutdowns {company_name} has announced in the United States from January 1, 2024, to the present?"

WEB_USER_PROMPT_EMP = "Research extensively and find all publicly announced plans for employment creation, hiring, employment reduction, or layoffs by {company_name} in the United States. The search period is from January 1, 2024, to the present."






