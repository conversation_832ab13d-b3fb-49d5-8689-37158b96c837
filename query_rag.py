import os
import traceback
from langchain_qdrant import Qdrant
from embeddings import * 
import qdrant_client
from langchain_core.documents import Document
from typing import List, Optional, Dict, Tuple, Any

# --- Main Retrieval Function ---
def retrieve_relevant_documents(query: str,
                                embedding_model,
                                collection_name: str, 
                                qdrant_url: str | None = None,
                                top_k: int = 5,
                                company_name: Optional[str] = None) -> List[Document]:
    """
    Embeds a query and performs filtered similarity search against the Qdrant vector store.
    """
    print(f"--- Starting Retrieval Process ---")

    if not embedding_model:
        print("ERROR: Failed to initialize embedding model via shared function. Aborting.")
        return None

    try:
        client = qdrant_client.QdrantClient(url=qdrant_url, timeout=80)
        vector_store = Qdrant(
            client=client,
            collection_name=collection_name,
            embeddings=embedding_model
        )
        print("Successfully connected to Qdrant.")
    except Exception as e:
        print(f"ERROR: Failed to connect to Qdrant: {e}")
        print(traceback.format_exc())
        return None

    # Apply filter if company_name is specified
    filter_ = None
    if company_name:
        from qdrant_client.models import Filter, FieldCondition, MatchValue
        filter_ = Filter(
            must=[
                FieldCondition(
                    key="metadata.company_name",  
                    match=MatchValue(value=company_name)
                )
            ]
        )
        print(f"Filter applied: company_name = '{company_name}'")

    try:
        print(f"\nPerforming similarity search for query: '{query}'")
        retrieved_docs = vector_store.similarity_search(
            query, 
            k=top_k, 
            filter=filter_
        )
        print(f"Search complete. Found {len(retrieved_docs)} documents.")
        return retrieved_docs

    except Exception as e:
        print(f"ERROR during similarity search: {e}")
        print(traceback.format_exc())
        return None

def gather_and_deduplicate_docs(
    company_name: str,
    embedding_model: Any,
    collection_name: str,
    qdrant_url: str,
    query_templates: List[str],
    top_k: int = 15
) -> List[Document]:
    """
    Gathers relevant documents for a given company by running multiple queries
    and de-duplicates the results while preserving metadata.

    Args:
        company_name: The name of the company to query for.
        embedding_model: The embedding model object.
        collection_name: The name of the Qdrant collection.
        qdrant_url: The URL for the Qdrant instance.
        top_k: The number of documents to retrieve for each query.

    Returns:
        A de-duplicated list of Document objects with their metadata intact.
    """
    all_retrieved_docs: List[Document] = []
    print(f"\nGathering documents for: {company_name}")

    for template in query_templates:
        final_user_question = template.format(company_name=company_name)
        docs = retrieve_relevant_documents(
            query=final_user_question,
            embedding_model=embedding_model,
            collection_name=collection_name,
            qdrant_url=qdrant_url,
            top_k=top_k,
            company_name=company_name
        )
        all_retrieved_docs.extend(docs)

    # De-duplicate based on source URL and page content
    unique_docs_map: Dict[Tuple[str, str], Document] = {}
    
    for doc in all_retrieved_docs:
        source_url = doc.metadata.get('source', 'N/A')
        unique_key = (source_url, doc.page_content)

        if unique_key not in unique_docs_map:
            unique_docs_map[unique_key] = doc

    deduplicated_docs = list(unique_docs_map.values())
    
    print(f"Total retrieved docs: {len(all_retrieved_docs)}")
    print(f"Unique docs after de-duplication: {len(deduplicated_docs)}")
    
    return deduplicated_docs





def format_docs_for_context_with_source(docs: Optional[List[Document]]) -> str:
    """
    Combines the page_content of retrieved documents into a single string,
    prefixing each chunk with its detailed metadata (company, form, date, section, URL).
    Suitable for LLM context when citation and detailed context are needed.

    Args:
        docs: A list of LangChain Document objects, or None.

    Returns:
        A single string containing the combined content with sources and metadata,
        or an empty string if the input is None or empty.
    """
    if not docs:
        return ""
        
    formatted_chunks = []
    for i, doc in enumerate(docs):
        # Safely access metadata, providing defaults
        metadata = doc.metadata if hasattr(doc, 'metadata') else {}
        
        # company_name = metadata.get("company_name", "N/A")
        # form_type = metadata.get("form_type", "N/A")
        # period_of_report = metadata.get("period_of_report", "N/A")
        # section_title = metadata.get("section", "N/A Section Title") # Human-readable title
        # Ensure we use 'source_filing_url' as established earlier
        source_url = metadata.get('source', 'N/A URL') 

        # Create the formatted string for this chunk, including the new metadata
        chunk_string = (
            f"--- Source Document {i+1} ---\n"
            # f"Company: {company_name}\n"
            # f"Form Type: {form_type}\n"
            # f"Report Date: {period_of_report}\n"
            # f"Section: {section_title}\n"
             f"Source URL: {source_url}\n\n"
            f"Content:\n{doc.page_content}\n"
            f"--- End Document {i+1} ---"
        )
        formatted_chunks.append(chunk_string)

    # Join the formatted chunks with a clear separator
    # Using double newline as in the original for separation between distinct source documents
    context_string = "\n\n".join(formatted_chunks)

    return context_string




def format_context_chunks(docs: List[Document]) -> str:
    """
    Format each document's content and metadata for display.
    Modified to include new metadata: company, form type, report date, section title,
    and use the correct source URL.
    """
    if not docs:
        return "No context documents found." # Handle empty list

    formatted_output = ""
    for i, doc in enumerate(docs, 1):
        # Safely access page_content
        content = doc.page_content if hasattr(doc, 'page_content') else "Content not available."

        # Safely access metadata and its keys
        metadata = doc.metadata if hasattr(doc, 'metadata') else {}
        
        # Get the new metadata fields, providing defaults if not found
        company_name = metadata.get("company_name", "N/A")
        form_type = metadata.get("form_type", "N/A")
        period_of_report = metadata.get("period_of_report", "N/A")
        # Use the human-readable section title from metadata, fallback to a generic name
        section_title = metadata.get("section", f"Details") 
        source_url = metadata.get("source", "#") # This is the new URL field

        # -- Start Original Formatting --
        formatted_output += "\n---\n\n" # Original separator

        # Modify the header to be more descriptive using the section title
        # Retaining the numbering for clarity if multiple chunks are shown
        formatted_output += f"### 📄 Context Chunk {i}: {section_title}\n"
        
        # Add the new metadata fields here
        formatted_output += f"**Company:** {company_name}\n"
        formatted_output += f"**Form Type:** {form_type}\n"
        formatted_output += f"**Report Date:** {period_of_report}\n"
        
        # Use the correct source_filing_url for the link
        # Make link text the filename for brevity
        link_display_text = source_url.split('/')[-1] if source_url != "#" else "Source"
        formatted_output += f"🔗 **Source Filing:** [{link_display_text}]({source_url})\n\n"
        
        formatted_output += f"**Content:**\n\n"
        
        # Using blockquote for content, as in your original, for better visual separation
        # Ensure content is split by newlines for proper blockquoting if it wasn't already
        content_lines = content.split('\n')
        quoted_content = "\n".join([f"> {line}" for line in content_lines])
        formatted_output += f"{quoted_content}\n\n"
        
        formatted_output += "----------------------------------------\n\n" # Original long separator
        # -- End Original Formatting --
        
    return formatted_output.strip()