import time
import time
import json
import requests
from typing import List, Dict, Any
import config
from logger import log
import utils
from embeddings import initialize_embedding_model
from query_rag import format_docs_for_context_with_source, gather_and_deduplicate_docs
from openai import OpenAI

# --- Initialize Global Components ---
try:
    log.info("Initializing OpenAI client...")
    openai_client = OpenAI(api_key=config.OPENAI_API_KEY)
    
    log.info("Initializing embedding model...")
    embedding_model = initialize_embedding_model()
    log.info("Embedding model initialized successfully.")

except Exception as e:
    log.error(f"FATAL: Failed to initialize clients or models. Error: {e}", exc_info=True)
    exit(1)


def retrieve_sec_data_for_topic(
    companies: List[Dict[str, Any]],
    topic: str
) -> List[Dict[str, Any]]:
    """
    Retrieves data from SEC filings for a list of companies on a specific topic.
    This function handles both 'capacity' and 'employment' topics.
    """
    if topic == 'capacity':
        log.info("Setting up for 'Capacity Expansion' data retrieval from SEC filings.")
        system_prompt = utils.CAP_EXP_SYSTEM_PROMPT_SEC
        query_templates = utils.CAPACITY_PLANS_QUERY_TEMPLATES
        user_prompt_template = utils.USER_PROMPT_TEMPLATE_CAP
        pydantic_schema = utils.SECCompanyExpansionData
        plan_types = ['capacity_expansion_plans', 'capacity_reduction_plans']
    elif topic == 'employment':
        log.info("Setting up for 'Employment Generation' data retrieval from SEC filings.")
        system_prompt = utils.EMP_GEN_SYSTEM_PROMPT_SEC
        query_templates = utils.EMPLOYMENT_PLANS_QUERY_TEMPLATES
        user_prompt_template = utils.USER_PROMPT_TEMPLATE_EMP
        pydantic_schema = utils.SECCompanyEmploymentData
        plan_types = ['employment_generation_plans', 'employment_reduction_plans']
    else:
        log.error(f"Invalid topic specified: {topic}. Must be 'capacity' or 'employment'.")
        return []

    processed_company_data = []

    for company in companies:
        company_name = company['name']
        log.info(f"--- [SEC RAG] Processing: {company_name} for topic: {topic} ---")
        
        try:
            # 1. Retrieve relevant documents from Qdrant
            log.info(f"Retrieving documents for {company_name} from Qdrant...")
            deduplicated_docs = gather_and_deduplicate_docs(
                company_name=company_name,
                embedding_model=embedding_model,
                collection_name=config.COLLECTION_NAME,
                qdrant_url=config.QDRANT_URL,
                query_templates=query_templates,
                top_k=15
            )

            if not deduplicated_docs:
                log.warning(f"No relevant documents found in Qdrant for {company_name}. Creating placeholder.")
                # Create a placeholder entry so it can be enriched by web search later.
                placeholder = {
                    "company": company_name,
                    plan_types[0]: [],
                    plan_types[1]: []
                }
                processed_company_data.append(placeholder)
                continue

            # 2. Construct the prompt for the LLM
            context_string = format_docs_for_context_with_source(deduplicated_docs)
            final_user_prompt = user_prompt_template.format(
                company_name=company_name,
                context_string=context_string
            )
            
            messages = [
                {"role": "system", "content": system_prompt},
                {"role": "user", "content": final_user_prompt}
            ]

            # 3. Query the LLM for structured data using client.responses.parse
            log.info(f"Sending request to OpenAI for {company_name}...")
            response = openai_client.responses.parse(
                model=config.OPENAI_GPT_MODEL,
                input=messages,
                text_format=pydantic_schema,
            )
            
            result = response.output_parsed
            company_data_dict = result.model_dump()
            
            # 4. Add extra fields (confidence_score, publishing_agency)
            log.info("Enriching extracted SEC data with confidence score and agency.")
            for plan_type in plan_types:
                for plan in company_data_dict.get(plan_type, []):
                    
                    plan['confidence_score'] = config.SEC_DATA_CONFIDENCE_SCORE
                    plan['publishing_agency'] = 'SEC'

            processed_company_data.append(company_data_dict)
            log.info(f"Successfully processed SEC RAG data for {company_name}. with {len(company_data_dict.get(plan_types[0], []))} expansion plans and {len(company_data_dict.get(plan_types[1], []))} reduction plans.")

        except Exception as e:
            log.error(f"Failed to process {company_name} via SEC RAG. Reason: {e}", exc_info=True)
            placeholder = {
                "company": company_name,
                plan_types[0]: [],
                plan_types[1]: []
            }
            processed_company_data.append(placeholder)
        
        time.sleep(1)

    return processed_company_data

import requests
import json

def retrieve_web_data_for_topic(
    companies: List[Dict[str, Any]],
    topic: str
) -> List[Dict[str, Any]]:
    """
    Retrieves data from a web search for a list of companies on a specific topic.

    Uses the Perplexity API to get structured JSON output.
    """
    if topic == 'capacity':
        log.info("Setting up for 'Capacity Expansion' data retrieval from Web Search.")
        system_prompt = utils.CAP_EXP_SYSTEM_PROMPT_WEB
        user_prompt_template = utils.WEB_USER_PROMPT_CAP
        pydantic_schema = utils.WebCompanyExpansionData
        plan_types = ['capacity_expansion_plans', 'capacity_reduction_plans']
    elif topic == 'employment':
        log.info("Setting up for 'Employment Generation' data retrieval from Web Search.")
        system_prompt = utils.EMP_GEN_SYSTEM_PROMPT_WEB
        user_prompt_template = utils.WEB_USER_PROMPT_EMP
        pydantic_schema = utils.WebCompanyEmploymentData
        plan_types = ['employment_generation_plans', 'employment_reduction_plans']
    else:
        log.error(f"Invalid topic specified: {topic}. Must be 'capacity' or 'employment'.")
        return []

    processed_web_data = []
    api_url = "https://api.perplexity.ai/chat/completions"
    headers = {
        "Authorization": config.PERPLEXITY_API_KEY
    }

    for company in companies:
        company_name = company['name']
        log.info(f"--- [WEB SEARCH] Processing: {company_name} for topic: {topic} ---")
        
        try:
            final_user_prompt = user_prompt_template.format(company_name=company_name)
            messages = [
                {"role": "system", "content": system_prompt},
                {"role": "user", "content": final_user_prompt},
            ]

            payload = {
                "model": config.PERPLEXITY_MODEL,
                "messages": messages,
                "search_after_date_filter": config.PERPLEXITY_START_DATE,
                "search_before_date_filter": config.PERPLEXITY_END_DATE,
                "response_format": {
                    "type": "json_schema",
                    "json_schema": {"schema": pydantic_schema.model_json_schema()}
                },
                "web_search_options": {
                    "search_context_size": "high"
                }
            }
            
            log.info(f"Sending request to Perplexity API for {company_name}...")
            response = requests.post(api_url, headers=headers, json=payload)
            response.raise_for_status() # Raise HTTPError for bad responses (4xx or 5xx)
            
            api_response_json = response.json()
            content_string = api_response_json["choices"][0]["message"]["content"]
            
            # The content is a JSON string, so we parse it again
            parsed_data = json.loads(content_string)
            processed_web_data.append(parsed_data)
            log.info(f"Successfully processed Web Search data for {company_name} with {len(parsed_data.get(plan_types[0], []))} expansion plans and {len(parsed_data.get(plan_types[1], []))} reduction plans.")

        except Exception as e:
            log.error(f"Failed to process {company_name} via Web Search. Reason: {e}", exc_info=True)
            # Create a placeholder on failure to maintain list integrity
            placeholder = {
                "company": company_name,
                plan_types[0]: [],
                plan_types[1]: []
            }
            processed_web_data.append(placeholder)

        time.sleep(2) # Perplexity has a stricter rate limit

    return processed_web_data

#------ Unifying function--------

def get_unified_data_for_companies(
    companies: List[Dict[str, Any]],
    topic: str
) -> List[Dict[str, Any]]:
    """
    Orchestrates data retrieval from both SEC RAG and Web Search, then
    unifies the results for each company.

    Args:
        companies (List[Dict[str, Any]]): List of company dicts from the dataloader.
        topic (str): The topic ('capacity' or 'employment').

    Returns:
        List[Dict[str, Any]]: A list of dictionaries, each containing the unified
                              data for one company.
    """
    log.info(f"--- Starting unified data retrieval for {len(companies)} companies on topic '{topic}' ---")
    
    sec_data_list = retrieve_sec_data_for_topic(companies, topic) 
    web_data_list = retrieve_web_data_for_topic(companies, topic)
    
    unified_results = []
    
    if topic == 'capacity':
        plan_types = ['capacity_expansion_plans', 'capacity_reduction_plans']
    else: 
        plan_types = ['employment_generation_plans', 'employment_reduction_plans']
        
    for i, company in enumerate(companies):
        company_name = company['name']
        # market_cap = company['market_cap_usd']
        log.info(f"Unifying data for {company_name}...")
        
        sec_data = sec_data_list[i]
        web_data = web_data_list[i]
        
        # if sec_data['company'] != company_name or web_data['company'] != company_name:
        #     log.error(f"Data mismatch for {company_name} at index {i}. Skipping unification.")
        #     continue
            
        unified_company_data = {
            "company": company_name,
            plan_types[0]: sec_data.get(plan_types[0], []) + web_data.get(plan_types[0], []),
            plan_types[1]: sec_data.get(plan_types[1], []) + web_data.get(plan_types[1], [])
        }
        log.info(f"Successfully unified data for {company_name} with {len(unified_company_data.get(plan_types[0], []))} expansion plans and {len(unified_company_data.get(plan_types[1], []))} reduction plans.")
        
        unified_results.append(unified_company_data)
        
    log.info(f"--- Unified data retrieval complete for topic '{topic}' ---")
    return unified_results


if __name__ == '__main__':
    log.info("--- Running data_retriever.py in test mode ---")
    
    test_companies = [
        # {'name': 'NVIDIA CORP', 'market_cap_usd': 20000},
        {'name': 'Tesla, Inc.', 'market_cap_usd': 20000}
    ]

    log.info("\n>>> Testing SEC Capacity Data Retrieval...")
    # capacity_data = retrieve_sec_data_for_topic(test_companies, 'capacity')
    # capacity_data = retrieve_web_data_for_topic(test_companies, 'capacity')
    capacity_data = get_unified_data_for_companies(test_companies, 'employment')
    if capacity_data:
        print("\n--- Sample SEC Capacity Data (first company) ---")
        # with open ("data.json", "w", encoding="utf-8") as f:
        #     json.dump(capacity_data, f, ensure_ascii=False, indent=2)
        print(json.dumps(capacity_data[0], indent=2))
        
    log.info("--- Test run complete ---")