import traceback
import torch
from langchain_huggingface import HuggingFaceEmbeddings
from langchain_core.documents import Document # For type hinting

def initialize_embedding_model(model_name: str = 'BAAI/bge-base-en-v1.5', device: str | None = None) -> HuggingFaceEmbeddings | None:
    """Initializes the HuggingFace embedding model."""
    print(f"\n--- Initializing Embedding Model: {model_name} ---")
    try:
        if device is None:
            #device = 'cpu'
            device = 'cuda' if torch.cuda.is_available() else 'mps' if torch.backends.mps.is_available() else 'cpu'
        print(f"Using device: {device}")

        embedding_model = HuggingFaceEmbeddings(
            model_name=model_name,
            model_kwargs={'device': device},
            # Consider normalize_embeddings=True for models like BGE/GTE if using cosine distance
            encode_kwargs={'normalize_embeddings': True}
        )
        print("Embedding model initialized successfully.")
        return embedding_model
    except ImportError:
         print("ERROR: Required library not found. Install langchain-huggingface, sentence-transformers, torch.")
         return None
    except Exception as e:
        print(f"ERROR initializing embedding model: {e}")
        print(traceback.format_exc())
        return None

def generate_embeddings(chunks: list[Document], embedding_model: HuggingFaceEmbeddings) -> list[list[float]] | None:
    """Generates embeddings for a list of Document chunks."""
    if not chunks or not embedding_model:
        print("Error: Cannot generate embeddings with empty chunks list or no embedding model.")
        return None

    print(f"\n--- Generating Embeddings for {len(chunks)} chunks ---")
    try:
        texts_to_embed = [chunk.page_content for chunk in chunks]
        embeddings_list = embedding_model.embed_documents(texts_to_embed)

        if embeddings_list and len(embeddings_list) == len(chunks):
             print(f"Successfully generated {len(embeddings_list)} embeddings.")
             print(f"Sample embedding dimension: {len(embeddings_list[0])}")
             return embeddings_list
        else:
             print(f"ERROR: Embedding failed or mismatch. Expected {len(chunks)}, Got {len(embeddings_list) if embeddings_list else 0}")
             return None

    except Exception as e:
        print(f"ERROR during embedding generation: {e}")
        print(traceback.format_exc())
        return None
    
    