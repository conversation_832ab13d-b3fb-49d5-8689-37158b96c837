import json
import pandas as pd
from datetime import datetime
from typing import List, Dict, Any
import config
from logger import log
import utils
from openai import OpenAI

try:
    log.info("Initializing OpenAI client for scorer...")
    openai_client = OpenAI(api_key=config.OPENAI_API_KEY)
except Exception as e:
    log.error(f"FATAL: Failed to initialize OpenAI client in scorer. Error: {e}", exc_info=True)
    exit(1)


def get_scores_for_companies(
    unified_company_data: List[Dict[str, Any]],
    topic: str,
    conversion_factor: float
) -> List[Dict[str, Any]]:
    """
    Takes a list of unified company data, sends it to an LLM for comparative scoring,
    and returns the scored results.

    Args:
        unified_company_data (List[Dict[str, Any]]): The list of unified data for a
                                                     set of companies (e.g., one category).
        topic (str): The topic to score on ('capacity' or 'employment').

    Returns:
        List[Dict[str, Any]]: A list of scored company data from the LLM.
                               Returns an empty list on failure.
    """
    if not unified_company_data:
        log.warning("Received an empty list of companies for scoring. Skipping.")
        return []

    log.info(f"Starting scoring process for {len(unified_company_data)} companies on topic '{topic}'.")

    if topic == 'capacity':
        system_prompt = utils.SCORES_SYSTEM_PROMPT_CAP
        system_prompt = system_prompt.format(conversion_factor=conversion_factor)
        output_schema = utils.scores_output_format_cap # Using the dict schema
    elif topic == 'employment':
        system_prompt = utils.SCORES_SYSTEM_PROMPT_EMP
        output_schema = utils.scores_output_format_emp # Using the dict schema
    else:
        log.error(f"Invalid topic for scoring: {topic}.")
        return []

    try:
        # Prepare the prompt for the scoring LLM
        # Serialize the list of unified data into a JSON string
        final_prompt = json.dumps(unified_company_data, indent=4)
        log.info("Sending unified data to OpenAI for final scoring...")
        
        response = openai_client.responses.create(
            model=config.OPENAI_GPT_MODEL,
            input=[
                {"role": "system", "content": system_prompt},
                {"role": "user", "content": final_prompt}
            ],
            temperature=0,
            text=output_schema 
        )

        # Parse the text output from the response
        scored_data = json.loads(response.output_text)
        
        log.info("Successfully received and parsed scores from OpenAI.")
        # The schema asks for a 'companies' key, so we return the list inside it.
        return scored_data.get("companies", [])

    except Exception as e:
        log.error(f"An error occurred during the scoring API call: {e}", exc_info=True)
        return []


def save_scores_to_excel(
    scored_data: List[Dict[str, Any]],
    industry_name: str,
    topic: str
):
    """
    Saves the final scored data to a timestamped Excel file.

    Args:
        scored_data (List[Dict[str, Any]]): The final list of scored companies,
                                            including their category.
        industry_name (str): The name of the industry for the filename.
        topic (str): The topic ('capacity' or 'employment') for the filename.
    """
    if not scored_data:
        log.warning("No scored data to save. Skipping file creation.")
        return

    try:
        # Convert the list of dictionaries to a pandas DataFrame
        df = pd.DataFrame(scored_data)
        
        # Ensure the output directory exists
        config.OUTPUT_DIR.mkdir(parents=True, exist_ok=True)
        
        # Create a unique, timestamped filename
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        # Sanitize industry name for filename
        safe_industry_name = industry_name.replace(" ", "_").replace("/", "-")
        filename = f"{topic.capitalize()}_Magnitudes_{safe_industry_name}_{timestamp}.xlsx"
        filepath = config.OUTPUT_DIR / filename
        
        # Save the DataFrame to an Excel file
        df.to_excel(filepath, index=False)
        log.info(f"Successfully saved final scores to: {filepath}")

    except Exception as e:
        log.error(f"Failed to save scores to Excel file. Error: {e}", exc_info=True)