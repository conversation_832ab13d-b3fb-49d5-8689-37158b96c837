import os
from pathlib import Path

# --- Project Paths ---
# absolute path to the project root directory.
PROJECT_ROOT = Path(__file__).resolve().parent

DATA_DIR = PROJECT_ROOT / "data"
LOGS_DIR = PROJECT_ROOT / "logs"
OUTPUT_DIR = PROJECT_ROOT / "output"


COMPANY_DATA_FILE = DATA_DIR / "US_Data_Matched_new_2.xlsx"

# --- API Keys & URLs ---
OPENAI_API_KEY = "********************************************************************************************************************************************************************"


PERPLEXITY_API_KEY = "Bearer 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"

# Qdrant Vector DB Configuration
QDRANT_URL = "http://*************:6333" 
COLLECTION_NAME = "SEC_chunks_indxx"     

# --- LLM and Model Configuration ---
OPENAI_GPT_MODEL = "gpt-4.1"       
PERPLEXITY_MODEL = "sonar-pro" 

# --- Confidence Scores ---
SEC_DATA_CONFIDENCE_SCORE = 1.0

#--- Perplexity search dates---
PERPLEXITY_START_DATE = "1/1/2023"
PERPLEXITY_END_DATE = "8/05/2025" # change it to today'date in future

# --- Pipeline Configuration ---
SCORING_BATCH_SIZE = 10

